<div>
    <!-- Modal Header Status -->
    <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-3">
            <div class="relative">
                <div class="avatar size-10 <?php echo e($isConnected ? 'ring-2 ring-pink-100 dark:ring-pink-900/50' : ''); ?>">
                    <div class="is-initial rounded-full <?php echo e($isConnected ? 'bg-gradient-to-br from-pink-500 to-purple-600' : 'bg-gradient-to-br from-slate-400 to-gray-500'); ?> text-white">
                        <i class="fab fa-instagram text-lg"></i>
                    </div>
                </div>
                <!--[if BLOCK]><![endif]--><?php if($isConnected): ?>
                    <div class="absolute -bottom-0.5 -right-0.5 size-3 rounded-full bg-pink-500 border border-white dark:border-navy-900"></div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
            <div>
                <h3 class="font-medium text-slate-800 dark:text-navy-100">Instagram</h3>
                <p class="text-xs <?php echo e($isConnected ? 'text-pink-600 dark:text-pink-400' : 'text-slate-500 dark:text-navy-300'); ?>">
                    <!--[if BLOCK]><![endif]--><?php if($sessionStatus === 'connected'): ?>
                        <i class="fas fa-circle text-pink-500 mr-1 animate-pulse"></i>
                        Connected and Active
                    <?php elseif($sessionStatus === 'connecting'): ?>
                        <i class="fas fa-spinner fa-spin text-blue-500 mr-1"></i>
                        Connecting...
                    <?php else: ?>
                        <i class="fas fa-circle text-slate-400 mr-1"></i>
                        Not Connected
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </p>
            </div>
        </div>
        <!--[if BLOCK]><![endif]--><?php if($isConnected): ?>
            <div class="badge bg-pink-100 text-pink-700 dark:bg-pink-900/30 dark:text-pink-300 px-2 py-1 rounded-full text-xs font-medium">
                <div class="size-1.5 rounded-full bg-pink-500 mr-1 animate-pulse"></div>
                Online
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Modal Body Content -->
    <div class="space-y-4 mb-6">

        <!--[if BLOCK]><![endif]--><?php if(!$isConnected): ?>
            <!-- Connect Instagram Section -->
            <div class="space-y-4">
                <div class="bg-white dark:bg-navy-700 border border-slate-200 dark:border-navy-500 rounded-lg overflow-hidden">
                    <div class="p-6">
                        <div class="text-center space-y-4">
                            <!-- Instagram Icon -->
                            <div class="relative inline-block">
                                <div class="w-20 h-20 mx-auto bg-gradient-to-br from-pink-100 to-purple-100 dark:from-pink-900/30 dark:to-purple-900/30 rounded-2xl flex items-center justify-center">
                                    <i class="fab fa-instagram text-3xl text-pink-500"></i>
                                </div>
                                <div class="absolute -bottom-1 -right-1 size-5 bg-slate-200 dark:bg-navy-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-link text-slate-500 dark:text-navy-300 text-xs"></i>
                                </div>
                            </div>

                            <!-- Content -->
                            <div class="space-y-2 max-w-xs mx-auto">
                                <h5 class="text-sm font-medium text-slate-800 dark:text-navy-100">Ready to Connect</h5>
                                <p class="text-xs text-slate-600 dark:text-navy-300">
                                    Connect your Instagram account to get started
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- Connected User Information -->
            <div class="space-y-4">
                <div class="bg-white dark:bg-navy-700 border border-slate-200 dark:border-navy-500 rounded-lg overflow-hidden">
                    <div class="p-4">
                        <div class="flex items-center space-x-4">
                            <!-- Profile Picture -->
                            <div class="relative">
                                <div class="avatar size-12 ring-2 ring-pink-100 dark:ring-pink-900/50">
                                    <!--[if BLOCK]><![endif]--><?php if(isset($userInfo['profile_pic_url']) && $userInfo['profile_pic_url']): ?>
                                        <img src="<?php echo e($userInfo['profile_pic_url']); ?>" alt="Profile Picture" class="rounded-full" />
                                    <?php else: ?>
                                        <div class="is-initial rounded-full bg-gradient-to-br from-pink-500 to-purple-600 text-white">
                                            <i class="fas fa-user"></i>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                                <div class="absolute -bottom-0.5 -right-0.5 size-4 rounded-full bg-pink-500 border border-white dark:border-navy-700 flex items-center justify-center">
                                    <i class="fas fa-check text-white text-xs"></i>
                                </div>
                            </div>

                            <!-- User Details -->
                            <div class="flex-1">
                                <div class="space-y-1">
                                    <h5 class="text-sm font-medium text-slate-800 dark:text-navy-100">
                                        <?php echo e($userInfo['full_name'] ?? $userInfo['username'] ?? 'Instagram User'); ?>

                                    </h5>
                                    <p class="text-xs text-slate-600 dark:text-navy-300">
                                        {{ $userInfo['username'] ?? 'unknown' }}
                                    </p>
                                    <!--[if BLOCK]><![endif]--><?php if($connectionTime): ?>
                                        <div class="flex items-center space-x-1 text-xs text-slate-500 dark:text-navy-400">
                                            <i class="fas fa-clock text-xs"></i>
                                            <span>Connected <?php echo e(\Carbon\Carbon::parse($connectionTime)->diffForHumans()); ?></span>
                                        </div>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>

                            <!-- Status Badge -->
                            <div class="text-right">
                                <div class="inline-flex items-center px-2 py-1 bg-pink-100 text-pink-700 dark:bg-pink-900/30 dark:text-pink-300 rounded-full text-xs font-medium">
                                    <div class="size-1.5 rounded-full bg-pink-500 mr-1 animate-pulse"></div>
                                    Active
                                </div>
                            </div>
                        </div>

                        <!--[if BLOCK]><![endif]--><?php if(isset($userInfo['followers_count'])): ?>
                            <div class="mt-4 pt-4 border-t border-slate-200 dark:border-navy-500">
                                <div class="flex items-center justify-center space-x-6">
                                    <div class="text-center">
                                        <div class="inline-flex items-center justify-center size-8 bg-slate-100 dark:bg-navy-600 rounded-lg mb-1">
                                            <i class="fas fa-users text-slate-600 dark:text-navy-300 text-sm"></i>
                                        </div>
                                        <p class="text-xs text-slate-500 dark:text-navy-400 uppercase tracking-wide font-medium">Followers</p>
                                        <p class="text-xs font-medium text-slate-700 dark:text-navy-200"><?php echo e(number_format($userInfo['followers_count'])); ?></p>
                                    </div>
                                    <div class="text-center">
                                        <div class="inline-flex items-center justify-center size-8 bg-pink-100 dark:bg-pink-900/30 rounded-lg mb-1">
                                            <i class="fas fa-user-plus text-pink-600 dark:text-pink-400 text-sm"></i>
                                        </div>
                                        <p class="text-xs text-slate-500 dark:text-navy-400 uppercase tracking-wide font-medium">Following</p>
                                        <p class="text-xs font-medium text-pink-600 dark:text-pink-400"><?php echo e(number_format($userInfo['following_count'])); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>

    <!-- Modal Footer Buttons -->
    <div class="border-t border-slate-200 dark:border-navy-500 pt-4">
        <!--[if BLOCK]><![endif]--><?php if($isConnected): ?>
            <div class="flex justify-center">
                <button wire:click="disconnect"
                    class="btn border border-red-300 text-red-600 hover:bg-red-50 focus:bg-red-50 active:bg-red-100 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:focus:bg-red-900/20 dark:active:bg-red-900/30 rounded-lg px-6 py-2 text-sm font-medium transition-all duration-200">
                    <i class="fas fa-unlink mr-2"></i>
                    Disconnect Instagram
                </button>
            </div>
        <?php else: ?>
            <div class="flex justify-center space-x-3">
                <button wire:click="connectInstagram"
                    wire:loading.attr="disabled"
                    wire:target="connectInstagram"
                    class="btn bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 focus:from-pink-600 focus:to-purple-700 active:from-pink-700 active:to-purple-800 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg px-6 py-2 text-sm font-medium transition-all duration-200">
                    <span wire:loading.remove wire:target="connectInstagram">
                        <i class="fab fa-instagram mr-2"></i>
                        Connect Instagram
                    </span>
                    <span wire:loading wire:target="connectInstagram">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        Connecting...
                    </span>
                </button>
                <!-- Temporary button for testing -->
                <button wire:click="simulateConnection"
                    class="btn bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 focus:from-pink-600 focus:to-purple-700 active:from-pink-700 active:to-purple-800 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200">
                    <i class="fas fa-check mr-2"></i>
                    Test Connection
                </button>
            </div>
        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    </div>
</div>
<?php /**PATH D:\Laravel_Projects\alhars\resources\views/livewire/settings/socials/instagram-settings.blade.php ENDPATH**/ ?>